<!--
  LandingPage.svelte
  
  Main navigation hub for the Insurance Portal application.
  
  Features:
  - Three prominent navigation buttons for different sections
  - Responsive CSS Grid layout with specified breakpoints
  - Semantic HTML structure with ARIA labels for accessibility
  - Professional styling with Tailwind CSS utility classes
  - Hover effects and smooth transitions
  
  Responsive Breakpoints:
  - Mobile (< 640px): Single column layout
  - Tablet (640px - 1024px): Responsive 2-column layout
  - Desktop (1024px - 1440px): Optimal 3-column layout
  - Large screens (> 1440px): Maintained spacing and alignment
  
  Navigation Options:
  1. Policy List - Navigate to existing policy list page
  2. Policy Detail - Navigate to policy detail page (placeholder)
  3. Claim Detail - Navigate to claim detail page (placeholder)
  
  Usage:
  <LandingPage {onNavigate} />
-->

<script>
  import { createEventDispatcher } from "svelte";

  const dispatch = createEventDispatcher();

  // Navigation handler
  function handleNavigation(page) {
    dispatch("navigate", { page });
  }

  // Navigation items configuration
  const navigationItems = [
    {
      id: "policy-list",
      title: "Policy List",
      description: "View and manage your insurance policies",
      icon: "📋",
      page: "policy-list",
      available: true,
    },
    {
      id: "policy-detail",
      title: "Policy Detail",
      description: "View detailed policy information",
      icon: "📄",
      page: "policy-detail",
      available: true,
    },
    {
      id: "claim-list",
      title: "Claim List",
      description: "View and track your insurance claims",
      icon: "📝",
      page: "claim-list",
      available: true,
    },
    // {
    //   id: "claim-detail",
    //   title: "Claim Detail",
    //   description: "Manage and track insurance claims",
    //   icon: "🔍",
    //   page: "claim-detail",
    //   available: true,
    // },
  ];
</script>

<main class="min-h-screen bg-gray-50 py-8 px-4 sm:px-6 lg:px-8">
  <!-- Page Header -->
  <!-- <header class="max-w-7xl mx-auto text-center mb-12">
    <h1 class="text-4xl font-bold text-gray-900 mb-4">
      Welcome to Insurance Portal
    </h1>
    <p class="text-xl text-gray-600 max-w-2xl mx-auto">
      Your comprehensive insurance management platform. Navigate to different
      sections using the options below.
    </p>
  </header> -->

  <!-- Navigation Grid -->
  <section class="max-w-7xl mx-auto" aria-label="Main navigation options">
    <div
      class="grid gap-8
             grid-cols-1
             sm:grid-cols-2
             lg:grid-cols-3
             xl:grid-cols-3"
      role="navigation"
      aria-label="Application sections"
    >
      {#each navigationItems as item (item.id)}
        <button
          class="bg-white rounded-xl shadow-lg hover:shadow-xl
                 transition-all duration-300 ease-in-out
                 hover:scale-105 transform
                 border border-gray-100
                 min-h-[280px] p-8
                 flex flex-col justify-between
                 text-left w-full
                 {item.available
            ? 'cursor-pointer'
            : 'opacity-75 cursor-not-allowed'}"
          disabled={!item.available}
          aria-labelledby="{item.id}-title"
          aria-describedby="{item.id}-description"
          on:click={() => item.available && handleNavigation(item.page)}
        >
          <!-- Card Header -->
          <div class="text-center mb-6">
            <div
              class="text-6xl mb-4 {item.available ? '' : 'grayscale'}"
              aria-hidden="true"
            >
              {item.icon}
            </div>
            <h2
              id="{item.id}-title"
              class="text-2xl font-bold text-gray-900 mb-3"
            >
              {item.title}
            </h2>
            <p
              id="{item.id}-description"
              class="text-gray-600 text-base leading-relaxed"
            >
              {item.description}
            </p>
          </div>

          <!-- Card Footer -->
          <div class="text-center">
            {#if item.available}
              <span
                class="inline-flex items-center px-6 py-3
                       bg-blue-600 hover:bg-blue-700
                       text-white font-semibold rounded-lg
                       transition-colors duration-200
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                role="button"
                aria-label="Navigate to {item.title}"
              >
                Open {item.title}
                <svg
                  class="ml-2 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </span>
            {:else}
              <span
                class="inline-flex items-center px-6 py-3
                       bg-gray-300 text-gray-500 font-semibold rounded-lg
                       cursor-not-allowed"
                aria-label="{item.title} - Coming Soon"
              >
                Coming Soon
                <svg
                  class="ml-2 w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </span>
            {/if}
          </div>
        </button>
      {/each}
    </div>
  </section>

  <!-- Additional Information -->
  <!-- <footer class="max-w-7xl mx-auto mt-16 text-center">
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
      <p class="text-gray-600 mb-4">
        Contact our support team for assistance with your insurance needs.
      </p>
      <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
        <a
          href="tel:+**************"
          class="inline-flex items-center px-4 py-2
                 bg-green-600 hover:bg-green-700
                 text-white font-medium rounded-md
                 transition-colors duration-200
                 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          aria-label="Call support at **************"
        >
          <svg
            class="mr-2 w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
          Call Support
        </a>
        <a
          href="mailto:<EMAIL>"
          class="inline-flex items-center px-4 py-2
                 bg-blue-600 hover:bg-blue-700
                 text-white font-medium rounded-md
                 transition-colors duration-200
                 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          aria-label="Email <NAME_EMAIL>"
        >
          <svg
            class="mr-2 w-4 h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            aria-hidden="true"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
            />
          </svg>
          Email Support
        </a>
      </div>
    </div>
  </footer> -->
</main>
