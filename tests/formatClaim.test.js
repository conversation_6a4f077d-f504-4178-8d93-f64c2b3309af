/**
 * formatClaim Function Tests
 * 
 * Tests for the formatClaim function to ensure proper data transformation
 * from API response format to component format, specifically for fixing
 * the Svelte keyed each block issue.
 */

import { describe, it, expect } from 'vitest';
import { formatClaim } from '../src/utils/formatters.js';
import { sampleClaimData } from './setup.js';

describe('formatClaim Function', () => {
  describe('Basic Data Transformation', () => {
    it('should transform API claim data to component format', () => {
      const result = formatClaim(sampleClaimData);
      
      expect(result).toBeDefined();
      expect(result.id).toBe(sampleClaimData.ClaimID);
      expect(result.claimNumber).toBe(sampleClaimData.ClaimNo);
      expect(result.type).toBe('Auto'); // Mapped from 'AUTO'
      expect(result.status).toBe('Approved'); // Mapped from 'APPROVED'
      expect(result.amount).toBe(sampleClaimData.ClaimAmount);
      expect(result.policyNumber).toBe(sampleClaimData.PolicyNo);
    });

    it('should provide unique id for keyed each blocks', () => {
      const result = formatClaim(sampleClaimData);
      
      // Should have a valid, non-undefined id
      expect(result.id).toBeDefined();
      expect(result.id).not.toBeNull();
      expect(result.id).toBe('CLM001');
    });

    it('should handle missing ClaimID by using ClaimNo', () => {
      const claimWithoutId = { ...sampleClaimData };
      delete claimWithoutId.ClaimID;
      
      const result = formatClaim(claimWithoutId);
      
      expect(result.id).toBe(claimWithoutId.ClaimNo);
    });

    it('should generate fallback id when both ClaimID and ClaimNo are missing', () => {
      const claimWithoutIds = { ...sampleClaimData };
      delete claimWithoutIds.ClaimID;
      delete claimWithoutIds.ClaimNo;
      
      const result = formatClaim(claimWithoutIds);
      
      expect(result.id).toBeDefined();
      expect(result.id).not.toBeNull();
      expect(typeof result.id).toBe('string');
    });
  });

  describe('Property Mapping', () => {
    it('should map all required properties for ClaimList component', () => {
      const result = formatClaim(sampleClaimData);
      
      // Properties used in ClaimList component
      expect(result.id).toBeDefined();
      expect(result.claimNumber).toBeDefined();
      expect(result.type).toBeDefined();
      expect(result.status).toBeDefined();
      expect(result.amount).toBeDefined();
      expect(result.deductible).toBeDefined();
      expect(result.dateOfLoss).toBeDefined();
      expect(result.policyNumber).toBeDefined();
      expect(result.description).toBeDefined();
    });

    it('should map claim types correctly', () => {
      const autoClaimData = { ...sampleClaimData, ClaimType: 'AUTO' };
      const healthClaimData = { ...sampleClaimData, ClaimType: 'HEALTH' };
      const homeClaimData = { ...sampleClaimData, ClaimType: 'HOME' };
      
      expect(formatClaim(autoClaimData).type).toBe('Auto');
      expect(formatClaim(healthClaimData).type).toBe('Health');
      expect(formatClaim(homeClaimData).type).toBe('Home');
    });

    it('should map claim statuses correctly', () => {
      const approvedClaim = { ...sampleClaimData, Status: 'APPROVED' };
      const pendingClaim = { ...sampleClaimData, Status: 'PENDING' };
      const rejectedClaim = { ...sampleClaimData, Status: 'REJECTED' };
      
      expect(formatClaim(approvedClaim).status).toBe('Approved');
      expect(formatClaim(pendingClaim).status).toBe('Pending');
      expect(formatClaim(rejectedClaim).status).toBe('Rejected');
    });

    it('should handle numeric values correctly', () => {
      const result = formatClaim(sampleClaimData);
      
      expect(typeof result.amount).toBe('number');
      expect(result.amount).toBe(2500);
      expect(typeof result.deductible).toBe('number');
    });
  });

  describe('Edge Cases', () => {
    it('should handle null input', () => {
      const result = formatClaim(null);
      expect(result).toBeNull();
    });

    it('should handle undefined input', () => {
      const result = formatClaim(undefined);
      expect(result).toBeNull();
    });

    it('should handle empty object', () => {
      const result = formatClaim({});
      
      expect(result).toBeDefined();
      expect(result.id).toBeDefined(); // Should generate fallback id
      expect(result.claimNumber).toBe('N/A');
      expect(result.description).toBe('N/A');
      expect(result.policyNumber).toBe('N/A');
    });

    it('should handle missing optional fields gracefully', () => {
      const minimalClaim = {
        ClaimID: 'TEST001',
        ClaimNo: 'TEST-001',
        ClaimType: 'AUTO',
        Status: 'APPROVED'
      };
      
      const result = formatClaim(minimalClaim);
      
      expect(result.id).toBe('TEST001');
      expect(result.claimNumber).toBe('TEST-001');
      expect(result.type).toBe('Auto');
      expect(result.status).toBe('Approved');
      expect(result.amount).toBe(0); // Default value
      expect(result.deductible).toBe(0); // Default value
    });
  });

  describe('Array Processing', () => {
    it('should work with array of claims', () => {
      const claims = [
        { ...sampleClaimData, ClaimID: 'CLM001' },
        { ...sampleClaimData, ClaimID: 'CLM002' },
        { ...sampleClaimData, ClaimID: 'CLM003' }
      ];
      
      const results = claims.map(formatClaim).filter(Boolean);
      
      expect(results).toHaveLength(3);
      expect(results[0].id).toBe('CLM001');
      expect(results[1].id).toBe('CLM002');
      expect(results[2].id).toBe('CLM003');
      
      // All should have unique ids
      const ids = results.map(r => r.id);
      const uniqueIds = [...new Set(ids)];
      expect(uniqueIds).toHaveLength(3);
    });
  });
});
